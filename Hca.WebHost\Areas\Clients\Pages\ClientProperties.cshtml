﻿@page "/clients/{clientId:guid}/properties"
@model Hca.WebHost.Areas.Clients.Pages.ClientPropertiesModel
@{
    Layout = "_LayoutClient";
    ViewData["Client"] = Model.Client;
}

<div class="col-xl-9 col-lg-8">
    <div class="row">
        <div class="col">
            <form hx-post="/clients/@Model.Client.Id/properties"
                  hx-target="#divPropertySearchResults"
                  hx-swap="innerHTML"
                  hx-trigger="load, change from:#ShowArchivedProperties, click from:#btnPropertiesSearch">
                <div class="form-group mb-4">
                    <input class="form-control mb-2" type="text" placeholder="Search properties" id="txtSearch" name="searchText">
                    <div class="d-flex">
                        <button class="btn btn-secondary" type="button" id="btnPropertiesSearch">
                            Search
                        </button>
                        <button class="btn btn-sm btn-secondary">Clear</button>
                        <div class="d-flex align-items-center">
                            &nbsp;&nbsp;&nbsp;
                            <select asp-for="ComplianceStatusFilter">
                                <option value="0">Show All</option>
                                <option value="1">Compliant</option>
                                <option value="2">Not compliant</option>
                                <option value="3">Next inspection is due within one month</option>
                                <option value="4">No documents uploaded and/or no ACMs identified or presumed</option>
                            </select>
                            &nbsp;&nbsp;&nbsp;
                            <input asp-for="ShowArchivedProperties" class="ml-4 mx-2" />Show Archived Properties
                        </div>
                    </div>
                    @Html.AntiForgeryToken()
                </div>
            </form>
        </div>
    </div>
</div>

<div class="row">
    <div class="col">
        <div class="card card-default">
            <div class="card-header">
                <small class="text-muted">PROPERTIES</small>
            </div>
            <div class="card-body" id="divPropertySearchResults">
            </div>
        </div>
    </div>
</div>

@section scripts {
    <script>
        $(() => {
            drawBreadcrumb([

                { url: '@Urls.Client(Model.Client.Id)', text: '@Model.Client.ClientName' },
                { text: 'Properties' }]);
        });

    </script>
}
