using Azure.Identity;
using Dapper;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Clients;
using Hca.Lib.Features.Documents.Publishing;
using Hca.Lib.Features.Projects.Documents.Publishing;
using Hca.Lib.Features.Reports.Publishing;
using Hca.Lib.Identity;
using Hca.Lib.Identity.Services;
using Hca.Lib.Services;
using Hca.Lib.Services.QRFileSpot;
using Hca.WebHost.Identity;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Azure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using SendGrid;
using SixLabors.ImageSharp.Web.DependencyInjection;
using System;
using System.Globalization;
using System.Reflection;

namespace Hca.WebHost;

public class Startup
{
    public Startup(IConfiguration configuration, IWebHostEnvironment webHostEnvironment)
    {
        Configuration = configuration;
        WebHostEnvironment = webHostEnvironment;
    }

    public IConfiguration Configuration { get; }
    public IWebHostEnvironment WebHostEnvironment { get; }

    // This method gets called by the runtime. Use this method to add services to the container.
    public void ConfigureServices(IServiceCollection services)
    {
        var appAssembly = typeof(Aggregate).GetTypeInfo().Assembly;

        var credential = new DefaultAzureCredential();

        services

            .AddHttpClient()

            .AddSingleton(new SendGridClient("*********************************************************************"))

            .AddScoped<ReportBuilder>()

            .AddScoped<ViewManager>()
            .AddTransient(typeof(IPipelineBehavior<,>), typeof(MediatorIdentityPipeline<,>))

            .AddScoped<ClientService>()
            .AddScoped<UserService>()
            .AddScoped<TemplateService>()
            .AddScoped<ValueListsService>()
            .AddScoped<InspectionService>()
            .AddScoped<QuoteService>()
            .AddScoped<DocumentService>()
            .AddScoped<ProjectService>()
            .AddScoped<PropertyCountsService>()
            .AddScoped<SiteCountsService>()
            .AddScoped<ClientCountsService>()

            .AddScoped<DocumentCompiler>()
            .AddScoped<QuoteDocumentBuilder>()

            .AddScoped<NewUserService>()
            .AddScoped<IPasswordHasher<HcaUser>, PasswordHasher<HcaUser>>()

            .AddSingleton<IDbHelper>((s) =>
            {
                return new DbHelper(new DbConfig
                {
                    ConnectionString = Configuration.GetConnectionString("DbConnectionString"),
                    AzureIdentityConnectionString = Configuration.GetConnectionString("DbAzureIdentity")
                });
            })

            .AddSingleton(new AzureTokenInterceptor(Configuration["AzureIdentityClientId"]))

            .AddScoped<IEventPublisher, EventPublisher>()

            .AddQrFileSpot(Configuration, WebHostEnvironment)

            .AddMediatR(appAssembly)

            .AddAzureClients(builder =>
            {
                // Add a KeyVault client
                // builder.AddSecretClient(Configuration.GetSection("KeyVault"));

                // Add a storage account client
                builder.AddBlobServiceClient(Configuration.GetSection("BlobStorage"));

                // Use the environment credential by default
                builder.UseCredential(credential);

                // Set up any default settings
                builder.ConfigureDefaults(Configuration.GetSection("AzureDefaults"));
            });

        services

            .AddSession()
            .AddHttpContextAccessor()

            .AddHcaIdentity()

            .AddLazyCache()
            .AddImageSharp();

        services.AddDataProtection()
            .SetApplicationName("spotlite")
                .PersistKeysToAzureBlobStorage(
                    new Uri(Configuration["BlobStorage:ServiceUri"] + Configuration["BlobStorage:DataProtectionPath"]),
                    credential)
                .ProtectKeysWithAzureKeyVault(
                    new Uri(Configuration["KeyVault:VaultUri"] + Configuration["KeyVault:DataProtectionPath"]),
                    credential);

        // allow large files to be uploaded
        services.Configure<IISServerOptions>(options =>
        {
            options.MaxRequestBodySize = int.MaxValue;
        });

        services.Configure<KestrelServerOptions>(options =>
        {
            options.Limits.MaxRequestBodySize = int.MaxValue; // if don't set default value is: 30 MB
        });

        services.Configure<FormOptions>(x =>
        {
            x.ValueLengthLimit = int.MaxValue;
            x.MultipartBodyLengthLimit = int.MaxValue; // if don't set default value is: 128 MB
            x.MultipartHeadersLengthLimit = int.MaxValue;
        });

        services

            .AddApplicationInsightsTelemetry()

            .AddRazorPages(options =>
            {
                options.Conventions.ConfigureFilter(new IgnoreAntiforgeryTokenAttribute());

                var policy = new AuthorizationPolicyBuilder().RequireAuthenticatedUser().Build();

                options.Conventions.ConfigureFilter(new AuthorizeFilter(policy));

                options.Conventions.ConfigureFilter(new TypeFilterAttribute(typeof(SelectedClientActionFilter)));

                options.Conventions.Add(new ClientPageRouteModelConvention());
            })

            //.AddViewOptions(options =>
            //{
            //    options.HtmlHelperOptions.CheckBoxHiddenInputRenderMode = CheckBoxHiddenInputRenderMode.None;
            //})

            .AddRazorRuntimeCompilation();
        
        services.AddControllers();

        SqlMapper.AddTypeHandler(new PropertyDocumentTypeSqlMapper());
        SqlMapper.AddTypeHandler(new NullableGuidHandler());
    }

    // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
    public static void Configure(IApplicationBuilder app, IWebHostEnvironment env)
    {
        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }
        else
        {
            app.UseExceptionHandler("/Error");
            // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
            app.UseHsts();
        }

        var enGB = "en-GB";
        var supportedCultures = new[] { new CultureInfo(enGB) };

        app.UseRequestLocalization(new RequestLocalizationOptions
        {
            DefaultRequestCulture = new RequestCulture(enGB),
            SupportedCultures = supportedCultures,
            SupportedUICultures = supportedCultures
        })

            .UseHttpsRedirection()

            .UseStaticFiles()

            .UseImageSharp()

            .UseSession()

            .UseRouting()

            .UseAuthentication()
            .UseAuthorization()

            .UseEndpoints(endpoints =>
            {
                endpoints.MapRazorPages();
                endpoints.MapControllers();
            });
    }
}
