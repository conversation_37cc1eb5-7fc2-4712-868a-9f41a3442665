﻿using Hca.Lib.Azure.Blob;
using Hca.WebHost.Pipeline;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System;
using System.Threading.Tasks;

namespace Hca.WebHost.Models;

[Authorize]
public class HcaPageModel : PageModel
{
    protected readonly IMediator _mediator;
    private readonly ViewManager _viewManager;

    public HcaPageModel(IMediator mediator, ViewManager viewManager)
    {
        _mediator = mediator;
        _viewManager = viewManager;
    }

    public bool InEditMode => PageContext.HttpContext.Request.Query[Constants.QuerystringModeKey] == Constants.EditMode;

    public async Task<string> GetBlobUrl(string containerName, string blobName)
    {
        if (_mediator == null) throw new ApplicationException("Page Service has not been passed to the base HcaPageModel");

        var sasUrl = await _mediator.Send(new GetSasUrl(containerName, blobName, false));
        return sasUrl.AbsoluteUri.ToString();
    }

    public Task<bool> IsHcaUser => _viewManager.IsHcaUser;

    public Task<bool> IsAdminUser => _viewManager.IsAdminUser;

    public Task<bool> IsClientUser => _viewManager.IsClientUser;

    public Guid ContactId => Guid.Parse(_viewManager.ContactId);
}