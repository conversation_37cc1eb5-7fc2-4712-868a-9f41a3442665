@page "/clients/{clientId:guid}/sites"
@model Hca.WebHost.Areas.Clients.Pages.ClientSitesModel
@using Hca.Lib.Features.Clients
@{
    Layout = "_LayoutClient";
    ViewData["Client"] = Model.Client;
}

<div class="col-xl-9 col-lg-8">
    <div class="row">
        <div class="col">
            <form hx-post="/clients/@Model.Client.Id/sites"
                  hx-target="#divSiteSearchResults"
                  hx-swap="innerHTML"
                  hx-trigger="load, click from:#btnSearchSites, change from:#ShowArchivedSites">
                <div class="form-group mb-4">
                    <input class="form-control mb-2" type="text" placeholder="Search sites" id="txtSearch" name="searchText">
                    <div class="d-flex">
                        <button class="btn btn-secondary"
                                type="button"
                                id="btnSearchSites">
                            Search
                        </button>
                        <button class="btn btn-sm btn-secondary">Clear</button>
                        <div class="d-flex align-items-center">
                            <input asp-for="ShowArchivedSites" class="ml-4 mx-2" />Show Archived Sites
                        </div>
                    </div>

                </div>
                @Html.AntiForgeryToken()
            </form>
        </div>
    </div>
</div>

<div class="row">
    <div class="col">
        <div class="card card-default">
            <div class="card-header">
                <small class="text-muted">SITES</small>
            </div>
            <div class="card-body" id="divSiteSearchResults">
            </div>
        </div>
    </div>
</div>

@section scripts{
    <script>

        $(() => {
            drawBreadcrumb([
                
                { url: '@Urls.Client(Model.Client.Id)', text: '@Model.Client.ClientName' },
                { text: 'Sites' }]);
        });

    </script>
}
