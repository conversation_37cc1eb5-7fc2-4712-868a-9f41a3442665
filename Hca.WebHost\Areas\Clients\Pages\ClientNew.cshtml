﻿@page "/clients/new"
@model Hca.WebHost.Areas.Clients.Pages.ClientNewModel

<form method="post" enctype="multipart/form-data">
    <div class="card card-default">
        <div class="card-header d-flex align-items-center">
            <div class="d-flex justify-content-center col">
                <div class="h4 m-0 text-center">Add New Client</div>
            </div>
        </div>
        <div class="card-body">
            <div class="row justify-content-center">
                <div class="col-12 col-sm-10">
                    <input type="hidden" asp-for="Client.Id" />
                    <input asp-for="Client.ClientName" row-label="Client Name" />
                    <select asp-for="Client.ClientType"
                            asp-items="Html.GetEnumSelectList<Hca.Lib.Features.Clients.ClientType>()"
                            row-label="Client Type">
                        <option selected="selected" value="">Please select</option>
                    </select>

                    <div class="form-group row">
                        <label class="col-xl-2 col-form-label" for="LogoUrl">Client Logo</label>
                        <div class="col-xl-10">
                            <input class="form-control filestyle"
                                   type="file"
                                   data-classbutton="btn btn-secondary"
                                   data-classinput="form-control inline"
                                   data-icon="&lt;span class='fa fa-upload mr'&gt;&lt;/span&gt;"
                                   asp-for="Logo" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-header d-flex align-items-center">
            <div class="d-flex justify-content-center col">
                <div class="h4 m-0 text-center">Principal Address</div>
            </div>
        </div>
        <div class="card-body">
            <div class="row py-4 justify-content-center">
                <div class="col-12 col-sm-10">
                    <input type="hidden" asp-for="Client.AddressId" />
                    <input type="text" asp-for="Client.Floor" row-label="Floor" />
                    <input type="text" asp-for="Client.Unit" row-label="Unit (number only)" />
                    <input type="text" asp-for="Client.BuildingName" row-label="Building Name" />
                    <input type="text" asp-for="Client.BuildingNumber" row-label="Building Number" />
                    <partial name="_AddressFormPartial" 
                             for="Address"
                             view-data='new ViewDataDictionary(ViewData) { { "showMap", false } }' />
                </div>
            </div>
        </div>
        <div class="card-footer">
            <a href="@Urls.Clients" class="btn" id="btnCancelNewClient">Cancel</a>
            <input type="submit" class="btn btn-outline-success" value="Save And Add Another" asp-page-handler="Another" />
            <input type="submit" class="btn btn-success" value="Save And View" asp-page-handler="" />
        </div>
    </div>
</form>

@section scripts{
    <script src="~/vendor/bootstrap-filestyle/src/bootstrap-filestyle.js"></script>
    <script>
        $(function () {
            drawBreadcrumb([{ text: 'Clients', url: '@Urls.ClientsNew', text: 'New' }]);
        });</script>
}
