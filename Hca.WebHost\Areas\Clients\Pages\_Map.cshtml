@model _MapModel

<div id="map" style="height: 400px; width: 100%;"></div>

@if (Model.ShowMapKey)
{
    <div id="mapKey" class="p-1" style="width: 100%; border: solid 1px black;display: flex; flex-wrap: wrap;">
        <div class="col-12 col-xl-6 p-1" style="display: inline-flex;">
            <svg width="25" height="25" xmlns="http://www.w3.org/2000/svg" style="overflow: visible;">
                <path fill="#27c24c" stroke="black" d="M 12,2 C 8.1340068,2 5,5.1340068 5,9 c 0,5.25 7,13 7,13 0,0 7,-7.75 7,-13 0,-3.8659932 -3.134007,-7 -7,-7 z" />
            </svg>
            <div class="ml-1">
                Compliant
            </div>
        </div>
        <div class="col-12 col-xl-6 p-1" style="display: inline-flex;">
            <svg width="25" height="25" xmlns="http://www.w3.org/2000/svg" style="overflow: visible;">
                <path fill="#f05050" stroke="black" d="M 12,2 C 8.1340068,2 5,5.1340068 5,9 c 0,5.25 7,13 7,13 0,0 7,-7.75 7,-13 0,-3.8659932 -3.134007,-7 -7,-7 z" />
            </svg>
            <div class="ml-1">
                Not compliant
            </div>
        </div>
        <div class="col-12 col-xl-6 p-1" style="display: inline-flex;">
            <svg width="25" height="25" xmlns="http://www.w3.org/2000/svg" style="overflow: visible;">
                <path fill="#ff902b" stroke="black" d="M 12,2 C 8.1340068,2 5,5.1340068 5,9 c 0,5.25 7,13 7,13 0,0 7,-7.75 7,-13 0,-3.8659932 -3.134007,-7 -7,-7 z" />
            </svg>
            <div class="ml-1">
                Next inspection is due within one month
            </div>
        </div>
        <div class="col-12 col-xl-6 p-1" style="display: inline-flex;">
            <svg width="25" height="25" xmlns="http://www.w3.org/2000/svg" style="overflow: visible;">
                <path fill="#768294" stroke="black" d="M 12,2 C 8.1340068,2 5,5.1340068 5,9 c 0,5.25 7,13 7,13 0,0 7,-7.75 7,-13 0,-3.8659932 -3.134007,-7 -7,-7 z" />
            </svg>
            <div class="ml-1">
                No documents uploaded and/or no ACMs identified or presumed
            </div>
        </div>
    </div>
}