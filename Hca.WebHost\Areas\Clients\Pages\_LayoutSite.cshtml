﻿@using Hca.WebHost.TagHelpers.NavMenu
@inject Hca.WebHost.Pipeline.ViewManager ViewManager
@inject Hca.Lib.Services.SiteCountsService SiteCountsService
@{
    var client = (Hca.Lib.Features.Clients.ClientDto)ViewData["Client"];
    var site = (Hca.Lib.Features.Clients.SiteDtoExtended)ViewData["Site"];
    var section = (string)ViewData["Section"];
    Guid? planId = ViewData["PlanId"] == null ? null : Guid.Parse(ViewData["PlanId"].ToString());

    var counts = await SiteCountsService.GetSiteCountsAsync(client.Id, site.Id);

    Layout = "_LayoutHorizontal";
    ViewData["Title"] = client.ClientName;
}

@section scripts {
    @RenderSection("scripts", required: false)
}

@section Styles {
    @RenderSection("Styles", required: false)
}

@section BodyArea {
    @RenderSection("BodyArea", required: false)
}

@functions {
    string ActiveSection(string currentSection)
    {
        return (string)ViewData["Section"] == currentSection ? "active" : @"";
    }
}

<div class="row">

    <div class="col-xl-3 col-lg-4">

        <!-- START menu-->
        <div class="mb-boxes collapse show">
            <div class="card card-default">
                <div class="card-body">
                    <ul class="nav nav-pills flex-column">
                        <li class="nav-item p-2">
                            @if (site.Deleted.HasValue)
                            {
                                <img src="~/images/Deleted.png" style="float: right;" />
                            }
                            else if (site.Archived.HasValue)
                            {
                                <img src="~/images/Archived.png" style="float: right;" />
                            }

                            <small class="text-muted">DETAILS</small>
                        </li>
                        <li class="nav-item nav-count @ActiveSection("Properties")">
                            <a class="nav-link d-flex align-items-center"
                               href="@Urls.ClientSiteProperties(client.Id, site.Id)">
                                <em class="fa-fw fa-lg fa fa-city mr-2"></em>
                                <span>Properties</span>
                            </a>
                            <span class="menu-item-count">@counts.PropertyCount</span>
                        </li>
                        <li class="nav-item @ActiveSection("Address")">
                            <a class="nav-link d-flex align-items-center"
                               href="@Urls.ClientSite(client.Id, site.Id)">
                                <em class="fa-fw fa-lg fa fa-building mr-2"></em>
                                <span>Address</span>
                            </a>
                        </li>
                        <li class="nav-item nav-count @ActiveSection("Plans")">
                            <a class="nav-link d-flex align-items-center"
                               href="@Urls.ClientSitePlans(client.Id, site.Id)">
                                <em class="fa-fw fa-lg fa fa-file mr-2"></em>
                                <span>Site Plans</span>
                            </a>
                            <span class="menu-item-count">@counts.SitePlanCount</span>
                        </li>
                    </ul>
                    @if (site.ArchiveReason.IsPopulated())
                    {
                        <h5 style="font-weight: normal; margin-top: 10px;">
                            <strong>Archive Reason: </strong>
                            @site.ArchiveReason
                        </h5>
                    }
                </div>
            </div>
        </div>

        <div class="collapse show">
            <div class="card card-default">
                <div class="card-body">
                    <ul class="nav nav-pills flex-column">
                        <li class="nav-item p-2">
                            <small class="text-muted">ACTIONS</small>
                        </li>

                        @if (site.Deleted.HasValue)
                        {
                            <nav-link text="Undelete Site" action="@Urls.ClientSiteUndelete(client.Id, site.Id)"></nav-link>
                        }
                        else if (site.Archived.HasValue)
                        {
                            <nav-link text="Reinstate Site" action="@Urls.ClientSiteReinstate(client.Id, site.Id)"></nav-link>

                            <nav-delete action="@Urls.ClientSiteDelete(client.Id, site.Id)"
                                        text="Delete Site"
                                        item-name="site @site.SiteName"></nav-delete>
                        }
                        else
                        {
                            <nav-link text="Add a New Property"
                                      action="@Urls.ClientPropertyNew(client.Id, site.Id)"
                                      type="@NavButtonType.Action"></nav-link>

                            <nav-link text="Upload a Site Plan"
                                      action="@Urls.ClientSitePlanUpload(client.Id, site.Id)"
                                      type="@NavButtonType.Action"></nav-link>

                            @if (planId.HasValue && section == "PlanEdit")
                            {
                                <nav-delete action="@Urls.ClientSitePlanDelete(client.Id, site.Id, planId.Value)"
                                            text="Delete Site Plan"
                                            item-name="plan"></nav-delete>
                            }
                            else
                            {
                                <nav-link text="Edit Site Details"
                                          action="@Urls.ClientSite(client.Id, site.Id).AddEditMode()"></nav-link>
                            }

                            <nav-archive action="@Urls.ClientSiteArchive(client.Id, site.Id)"
                                         text="Archive Site"
                                         item-name="site"></nav-archive>
                        }
                    </ul>
                </div>
            </div>
        </div>
        <!-- END menu-->

    </div>

    <div class="col-xl-9 col-lg-8">

        @RenderBody()

    </div>
</div>
