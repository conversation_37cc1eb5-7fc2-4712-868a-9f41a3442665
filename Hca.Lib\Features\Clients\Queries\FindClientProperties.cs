﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Clients.Queries
{
    public class FindClientProperties : PagedQuery<PropertyDtoExtended>
    {
        public FindClientProperties(
            Guid clientId,
            string searchText,
            int pageNum = 1,
            int pageSize = 20,
            bool isArchived = false) : base(pageNum, pageSize)
        {
            ClientId = clientId;
            SearchText = searchText;
            IsArchived = isArchived;
        }

        public Guid ClientId { get; }
        public string SearchText { get; }
        public bool IsArchived { get; }
    }

    public class FindClientPropertiesHandler : DapperRequestHandler<FindClientProperties, PagedDtoSet<PropertyDtoExtended>>
    {
        public FindClientPropertiesHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<PagedDtoSet<PropertyDtoExtended>> OnHandleAsync(IDbHelper db, FindClientProperties request)
        {
            var whereClause = string.IsNullOrWhiteSpace(request.SearchText)
                ? " "
                : " AND Code LIKE '%' + @SearchText + '%' ";
            var sql =

                $"SELECT " +
                $"  {TableNames.Properties}.*, " +
                $"  {nameof(PropertyDtoExtended.SiteName)}, " +
                $"  (SELECT " +
                $"      MAX({nameof(PropertyDocumentDto.NextInspection)}) " +
                $"      FROM {TableNames.PropertyDocuments} " +
                $"      WHERE {nameof(PropertyDocumentDto.PropertyId)} = {TableNames.Properties}.{nameof(PropertyDtoExtended.Id)}) " +
                $"      AS {nameof(PropertyDtoExtended.NextInspection)} " +
                $"FROM {TableNames.Properties} " +
                $"LEFT JOIN {TableNames.Sites} " +
                $"  ON {TableNames.Sites}.{nameof(SiteDtoExtended.Id)} = {nameof(PropertyDtoExtended.SiteId)} " +
@"WHERE ClientId=@ClientId 
AND IsPropertyArchived IS " + (request.IsArchived ? "NOT NULL" : "NULL") + whereClause + @"
ORDER BY COALESCE(SiteName, '') + COALESCE(PropertyCode, '') + COALESCE(BuildingName, '') + COALESCE(Unit, '') + COALESCE(Floor, '') +
COALESCE(BuildingNumber, '') + COALESCE(StreetName, '') + COALESCE(Town, '') + COALESCE(City, '') + COALESCE(County, '') + 
COALESCE(Postcode, '') + COALESCE(Country, '')
OFFSET @Offset ROWS
FETCH NEXT @PageSize ROWS ONLY

SELECT COUNT(Id)
FROM tblProperties
WHERE ClientId=@ClientId 
AND Archived IS " + (request.IsArchived ? "NOT NULL" : "NULL") + whereClause;

            var (Items, Total) = await db.QueryPageAsync<PropertyDtoExtended>(
                sql,
                request.Page,
                request.PageSize,
                new {
                    request.ClientId,
                    request.SearchText,
                    request.IsArchived,
                });

            return PagedDtoSet.From(Items, request.Page, request.PageSize, Total);
        }
    }
}
