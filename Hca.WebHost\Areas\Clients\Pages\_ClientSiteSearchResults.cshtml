@using Hca.Lib.Features.Clients;
@model ClientSitesModel

<div class="row">
    <div class="col-lg-6">

        @if (Model.Sites.Any())
        {
            <table class="table table-striped table-bordered table-hover">
                <tbody id="siteSearchResults">
                    @foreach (var site in Model.Sites.OrderBy(l => l.SiteName))
                    {
                        <tr style="cursor: pointer;"
                            onclick="window.location.href='@Urls.ClientSiteProperties(Model.Client.Id, site.Id)';">
                            <td>@site.GetDisplayText()</td>
                        </tr>
                    }
                </tbody>
            </table>
        }
        else
        {
            <p>No sites found</p>
        }

        @if (Model.ShowArchivedSites)
        {
            <br />
            <small class="text-muted">ARCHIVED SITES</small>
            <br />

            <br />

            @if (Model.ArchivedSites.Any())
            {
                <table class="table table-striped table-bordered table-hover">
                    <tbody>
                        @foreach (var site in Model.ArchivedSites.OrderBy(l => l.SiteName))
                        {
                            <tr style="cursor: pointer;"
                                data-toggle="tooltip"
                                data-placement="top"
                                data-html="true"
                                data-original-title="@(site.ArchiveReason.IsPopulated() ? $"<strong>Archive Reason</strong>: {site.ArchiveReason}" : "")"
                                onclick="window.location.href='@Urls.ClientSiteProperties(Model.Client.Id, site.Id)';">
                                <td>@site.GetDisplayText()</td>
                            </tr>
                        }
                    </tbody>
                </table>
            }
            else
            {
                <p>No archived sites found matching the current criteria</p>
            }
        }

    </div>
    <div class="col-lg-6">
        <partial name="_ListMapScripts" model='new _ListMapScriptsModel(Model.Client, Model.Sites){ PointerColour = "#68BBE3", ShowMapNumbers = true }' />
        <partial name="_Map" model='new _MapModel{ ShowMapKey = false }' />
    </div>
</div>

<script>
    $('[data-toggle="tooltip"]').tooltip({
        container: 'body'
    });
</script>
